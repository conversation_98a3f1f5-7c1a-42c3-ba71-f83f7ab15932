#!/usr/bin/env node

/**
 * EdgeOne CDN缓存清除脚本
 * 在构建完成后自动清除腾讯EdgeOne CDN缓存
 */

const https = require('https');
const crypto = require('crypto');

// EdgeOne API配置
const EDGEONE_CONFIG = {
  secretId: process.env.EDGEONE_SECRET_ID,
  secretKey: process.env.EDGEONE_SECRET_KEY,
  zoneId: process.env.EDGEONE_ZONE_ID,
  region: process.env.EDGEONE_REGION || 'ap-beijing',
  service: 'teo',
  version: '2022-09-01',
  action: 'CreatePurgeTask'
};

// 验证必需的环境变量
function validateConfig() {
  const required = ['EDGEONE_SECRET_ID', 'EDGEONE_SECRET_KEY', 'EDGEONE_ZONE_ID'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ 缺少必需的环境变量:');
    missing.forEach(key => console.error(`   - ${key}`));
    console.error('\n请在 .env.local 文件中设置这些变量，或在部署环境中配置。');
    process.exit(1);
  }
}

// 生成腾讯云API签名
function generateSignature(params, timestamp) {
  const { secretId, secretKey, region, service, version, action } = EDGEONE_CONFIG;
  
  // 1. 拼接规范请求串
  const httpRequestMethod = 'POST';
  const canonicalUri = '/';
  const canonicalQueryString = '';
  const canonicalHeaders = `content-type:application/json; charset=utf-8\nhost:teo.tencentcloudapi.com\nx-tc-action:${action.toLowerCase()}\n`;
  const signedHeaders = 'content-type;host;x-tc-action';
  const hashedRequestPayload = crypto.createHash('sha256').update(JSON.stringify(params)).digest('hex');
  
  const canonicalRequest = [
    httpRequestMethod,
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    hashedRequestPayload
  ].join('\n');
  
  // 2. 拼接待签名字符串
  const algorithm = 'TC3-HMAC-SHA256';
  const date = new Date(timestamp * 1000).toISOString().substr(0, 10);
  const credentialScope = `${date}/${service}/tc3_request`;
  const hashedCanonicalRequest = crypto.createHash('sha256').update(canonicalRequest).digest('hex');
  
  const stringToSign = [
    algorithm,
    timestamp,
    credentialScope,
    hashedCanonicalRequest
  ].join('\n');
  
  // 3. 计算签名
  const secretDate = crypto.createHmac('sha256', `TC3${secretKey}`).update(date).digest();
  const secretService = crypto.createHmac('sha256', secretDate).update(service).digest();
  const secretSigning = crypto.createHmac('sha256', secretService).update('tc3_request').digest();
  const signature = crypto.createHmac('sha256', secretSigning).update(stringToSign).digest('hex');
  
  // 4. 拼接 Authorization
  const authorization = `${algorithm} Credential=${secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
  
  return authorization;
}

// 调用EdgeOne API清除缓存
function clearEdgeOneCache(urls = []) {
  return new Promise((resolve, reject) => {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // 构建请求参数
    const params = {
      ZoneId: EDGEONE_CONFIG.zoneId,
      Type: urls.length > 0 ? 'url' : 'purge_all',
      Method: 'invalidate'
    };
    
    // 如果指定了URL，添加到参数中
    if (urls.length > 0) {
      params.Targets = urls;
    }
    
    const authorization = generateSignature(params, timestamp);
    const payload = JSON.stringify(params);
    
    const options = {
      hostname: 'teo.tencentcloudapi.com',
      port: 443,
      path: '/',
      method: 'POST',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json; charset=utf-8',
        'Host': 'teo.tencentcloudapi.com',
        'X-TC-Action': EDGEONE_CONFIG.action,
        'X-TC-Timestamp': timestamp,
        'X-TC-Version': EDGEONE_CONFIG.version,
        'X-TC-Region': EDGEONE_CONFIG.region
      }
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (response.Response.Error) {
            reject(new Error(`EdgeOne API错误: ${response.Response.Error.Message}`));
          } else {
            resolve(response.Response);
          }
        } catch (error) {
          reject(new Error(`解析响应失败: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });
    
    req.write(payload);
    req.end();
  });
}

// 主函数
async function main() {
  console.log('🚀 开始清除EdgeOne CDN缓存...');
  
  try {
    // 验证配置
    validateConfig();
    
    // 获取要清除的URL列表（可以从命令行参数或环境变量获取）
    const urlsToCache = process.argv.slice(2);
    
    if (urlsToCache.length > 0) {
      console.log(`📋 清除指定URL缓存: ${urlsToCache.join(', ')}`);
    } else {
      console.log('🧹 清除所有缓存');
    }
    
    // 调用API清除缓存
    const result = await clearEdgeOneCache(urlsToCache);
    
    console.log('✅ EdgeOne CDN缓存清除成功!');
    console.log(`📝 任务ID: ${result.JobId || result.TaskId || 'N/A'}`);
    
    // 如果有失败的URL，显示详情
    if (result.FailedList && result.FailedList.length > 0) {
      console.log('⚠️  部分URL清除失败:');
      result.FailedList.forEach(item => {
        console.log(`   - ${item.Url}: ${item.Reason}`);
      });
    }
    
  } catch (error) {
    console.error('❌ EdgeOne CDN缓存清除失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { clearEdgeOneCache };
